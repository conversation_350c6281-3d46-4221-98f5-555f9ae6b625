<template>
  <div class="course-homework">
    <h3>作业管理</h3>
    <p>课程ID: {{ courseId }}</p>
    <p>作业管理功能正在开发中...</p>
  </div>
</template>

<script setup>
// 定义props
const props = defineProps({
  courseId: {
    type: [String, Number],
    required: true
  }
});

// 定义emits
const emit = defineEmits(['refresh']);

console.log('作业管理组件加载，课程ID:', props.courseId);
</script>

<style scoped>
.course-homework {
  padding: 2rem;
  background-color: #ffffff;
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}
</style>