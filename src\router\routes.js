import { getCurrentUser } from '@/api/auth';

const routes = [
  {
    path: '/',
    name: 'home',
    component: () => import('@/pages/MainHomePage.vue'),
    meta: {
      title: 'HiProf 智能教育平台'
    }
  },
  {
    path: '/ai-assistant',
    name: 'ai-assistant',
    component: () => import('@/pages/AIAssistantPage.vue'),
    meta: {
      title: 'AI智能助手'
    }
  },
  {
    path: '/teacher',
    name: 'teacher',
    component: () => import('@/layouts/TeacherLayout.vue'),
    meta: {
      title: '教师教学助手',
      requiresAuth: true,
      role: 'teacher'
    },
    children: [
      {
        path: '',
        redirect: '/teacher/lesson-generator'
      },
      {
        path: 'lesson-generator',
        name: 'lesson-generator',
        component: () => import('@/pages/teacher/LessonGeneratorPage.vue'),
        meta: {
          title: '教案生成'
        }
      },
      {
        path: 'lesson-create',
        name: 'lesson-create',
        component: () => import('@/pages/teacher/LessonCreatePage.vue'),
        meta: {
          title: '创建教案'
        }
      },
      {
        path: 'lesson-list',
        name: 'lesson-list',
        component: () => import('@/pages/teacher/LessonListPage.vue'),
        meta: {
          title: '教案列表'
        }
      },
      {
        path: 'lesson-edit/:id',
        name: 'lesson-edit',
        component: () => import('@/pages/teacher/LessonEditPage.vue'),
        meta: {
          title: '编辑教案'
        }
      },
      {
        path: 'ppt-generator',
        name: 'ppt-generator',
        component: () => import('@/pages/teacher/PPTGeneratorPage.vue'),
        meta: {
          title: 'PPT生成'
        }
      },
      {
        path: 'materials',
        name: 'materials',
        component: () => import('@/pages/teacher/MaterialsPage.vue'),
        meta: {
          title: '相关资料'
        }
      }
    ]
  },
  {
    path: '/teacher/my',
    name: 'teacher-my',
    component: () => import('@/pages/teacher/Teacher_Mypage.vue'),
    meta: {
      title: '教师个人中心',
      requiresAuth: true,
      role: 'teacher'
    }
  },
  {
    path: '/teacher/class/:id',
    name: 'teacher-class-detail',
    component: () => import('@/pages/teacher/ClassDetail.vue'),
    meta: {
      title: '班级详情',
      requiresAuth: true,
      role: 'teacher'
    }
  },
  {
    path: '/teacher/course/:courseId',
    name: 'teacher-course-detail',
    component: () => import('@/components/teacher/modules/TeacherCourseDetail/TeacherCourseDetail.vue'),
    meta: {
      title: '课程详情',
      requiresAuth: true,
      role: 'teacher'
    },
    props: true // 允许将路由参数作为props传递给组件
  },
  {
    path: '/student',
    name: 'student',
    component: () => import('@/layouts/StudentLayout.vue'),
    meta: {
      title: '学生学习平台',
      requiresAuth: true,
      role: 'student'
    },
    children: [
      {
        path: '',
        redirect: '/student/my'
      },
      {
        path: 'my',
        name: 'student-my',
        component: () => import('@/pages/student/Student_Mypage.vue'),
        meta: {
          title: '学生个人中心'
        }
      }
    ]
  },
  {
    path: '/outline/:id?',
    name: 'outline',
    component: () => import('@/pages/OutlinePage.vue'),
    meta: {
      title: '知识图谱大纲',
      requiresAuth: true
    }
  },
  {
    path: '/outline-edit/:id',
    name: 'outline-edit',
    component: () => import('@/pages/OutlinePage.vue'),
    meta: {
      title: '编辑知识图谱大纲',
      requiresAuth: true,
      isEditMode: true
    }
  },
  {
    path: '/graph',
    name: 'graph',
    component: () => import('@/pages/GraphPage.vue'),
    meta: {
      title: '知识图谱'
    }
  },
  {
    path: '/graph/:id',
    name: 'course-graph',
    component: () => import('@/pages/GraphPage.vue'),
    meta: {
      title: '课程知识图谱'
    }
  },
  {
    path: '/my',
    name: 'my',
    redirect: () => {
      // 根据用户角色重定向到对应的个人中心
      try {
        const user = getCurrentUser();
        if (user && user.role === 'teacher') {
          return '/teacher/my';
        } else if (user && user.role === 'student') {
          return '/student/my';
        }
      } catch (error) {
        console.error('获取用户信息失败:', error);
      }
      // 默认重定向到登录页
      return '/login';
    },
    meta: {
      title: '个人中心',
      requiresAuth: true
    }
  },
  {
    path: '/login',
    name: 'login',
    component: () => import('@/pages/LoginPage.vue'),
    meta: {
      title: '登录',
      hideForAuth: true
    }
  },
  {
    path: '/register',
    name: 'register',
    component: () => import('@/pages/RegisterPage.vue'),
    meta: {
      title: '注册',
      hideForAuth: true
    }
  },
  {
    path: '/forgot-password',
    name: 'forgot-password',
    component: () => import('@/pages/ForgotPasswordPage.vue'),
    meta: {
      title: '找回密码',
      hideForAuth: true
    }
  },
  {
    path: '/reset-password',
    name: 'reset-password',
    component: () => import('@/pages/ResetPasswordPage.vue'),
    meta: {
      title: '重置密码',
      hideForAuth: true
    }
  },
  {
    path: '/:pathMatch(.*)*',
    name: 'not-found',
    component: () => import('@/pages/MainHomePage.vue'), // 使用新的主页作为占位符
    meta: {
      title: '页面未找到'
    }
  }
];

export default routes; 