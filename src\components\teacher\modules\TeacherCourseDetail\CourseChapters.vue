<template>
  <div class="course-chapters">
    <!-- 页面标题 -->
    <div class="section-header">
      <h2 class="section-title">章节管理</h2>
      <div class="section-actions">
        <button class="btn btn-primary" @click="addNewChapter">
          <i class="btn-icon plus-icon"></i>
          新增章节
        </button>
        <button class="btn btn-secondary" @click="reorderChapters">
          <i class="btn-icon reorder-icon"></i>
          调整顺序
        </button>
      </div>
    </div>

    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p class="loading-text">正在加载章节列表...</p>
    </div>

    <!-- 章节列表 -->
    <div v-else-if="chapters.length > 0" class="chapters-list">
      <div
        v-for="(chapter, index) in chapters"
        :key="chapter.id"
        class="chapter-item"
        :class="{ expanded: chapter.expanded }"
      >
        <!-- 章节头部 -->
        <div class="chapter-header" @click="toggleChapter(chapter.id)">
          <div class="chapter-info">
            <span class="chapter-number">第{{ index + 1 }}章</span>
            <h3 class="chapter-title">{{ chapter.title }}</h3>
            <span class="chapter-status" :class="chapter.status">
              {{ getStatusText(chapter.status) }}
            </span>
          </div>
          <div class="chapter-actions">
            <button class="action-btn" @click.stop="editChapter(chapter)">
              <i class="edit-icon"></i>
            </button>
            <button class="action-btn" @click.stop="deleteChapter(chapter.id)">
              <i class="delete-icon"></i>
            </button>
            <i class="expand-icon" :class="{ rotated: chapter.expanded }"></i>
          </div>
        </div>

        <!-- 章节内容（展开时显示） -->
        <div v-if="chapter.expanded" class="chapter-content">
          <div class="chapter-details">
            <p class="chapter-description">{{ chapter.description }}</p>
            <div class="chapter-meta">
              <span class="meta-item">
                <i class="time-icon"></i>
                学时：{{ chapter.hours }}
              </span>
              <span class="meta-item">
                <i class="difficulty-icon"></i>
                难度：{{ getDifficultyText(chapter.difficulty) }}
              </span>
              <span class="meta-item">
                <i class="progress-icon"></i>
                进度：{{ chapter.progress }}%
              </span>
            </div>
          </div>

          <!-- 小节列表 -->
          <div v-if="chapter.sections && chapter.sections.length > 0" class="sections-list">
            <h4 class="sections-title">小节列表</h4>
            <div
              v-for="(section, sectionIndex) in chapter.sections"
              :key="section.id"
              class="section-item"
            >
              <span class="section-number">{{ index + 1 }}.{{ sectionIndex + 1 }}</span>
              <span class="section-title">{{ section.title }}</span>
              <span class="section-duration">{{ section.duration }}分钟</span>
              <div class="section-actions">
                <button class="action-btn small" @click="editSection(section)">
                  <i class="edit-icon"></i>
                </button>
                <button class="action-btn small" @click="deleteSection(section.id)">
                  <i class="delete-icon"></i>
                </button>
              </div>
            </div>
            <button class="btn btn-outline btn-small" @click="addSection(chapter.id)">
              <i class="plus-icon"></i>
              添加小节
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- 空状态 -->
    <div v-else class="empty-state">
      <div class="empty-icon">📚</div>
      <h3 class="empty-title">暂无章节</h3>
      <p class="empty-description">开始创建您的第一个章节吧</p>
      <button class="btn btn-primary" @click="addNewChapter">
        <i class="plus-icon"></i>
        新增章节
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';

// 定义props
const props = defineProps({
  courseId: {
    type: [String, Number],
    required: true
  }
});

// 定义emits
const emit = defineEmits(['refresh']);

// 响应式数据
const loading = ref(false);
const chapters = ref([]);

// 加载章节列表
const loadChapters = async () => {
  loading.value = true;
  try {
    // TODO: 实际的API调用
    // const response = await getChaptersByCourseId(props.courseId);
    // chapters.value = response.data;
    
    // 模拟数据
    chapters.value = [
      {
        id: 1,
        title: '函数与极限',
        description: '介绍函数的基本概念和极限理论',
        status: 'published',
        hours: 8,
        difficulty: 'medium',
        progress: 100,
        expanded: false,
        sections: [
          { id: 1, title: '函数的概念', duration: 45 },
          { id: 2, title: '极限的定义', duration: 50 },
          { id: 3, title: '极限的性质', duration: 40 }
        ]
      },
      {
        id: 2,
        title: '导数与微分',
        description: '学习导数的概念、计算方法和应用',
        status: 'draft',
        hours: 10,
        difficulty: 'hard',
        progress: 60,
        expanded: false,
        sections: [
          { id: 4, title: '导数的定义', duration: 50 },
          { id: 5, title: '求导法则', duration: 60 }
        ]
      }
    ];
  } catch (error) {
    console.error('加载章节列表失败:', error);
  } finally {
    loading.value = false;
  }
};

// 切换章节展开状态
const toggleChapter = (chapterId) => {
  const chapter = chapters.value.find(c => c.id === chapterId);
  if (chapter) {
    chapter.expanded = !chapter.expanded;
  }
};

// 获取状态文本
const getStatusText = (status) => {
  const statusMap = {
    'draft': '草稿',
    'published': '已发布',
    'archived': '已归档'
  };
  return statusMap[status] || '未知';
};

// 获取难度文本
const getDifficultyText = (difficulty) => {
  const difficultyMap = {
    'easy': '简单',
    'medium': '中等',
    'hard': '困难'
  };
  return difficultyMap[difficulty] || '未知';
};

// 新增章节
const addNewChapter = () => {
  console.log('新增章节');
  // TODO: 实现新增章节逻辑
};

// 编辑章节
const editChapter = (chapter) => {
  console.log('编辑章节:', chapter);
  // TODO: 实现编辑章节逻辑
};

// 删除章节
const deleteChapter = (chapterId) => {
  console.log('删除章节:', chapterId);
  // TODO: 实现删除章节逻辑
};

// 调整章节顺序
const reorderChapters = () => {
  console.log('调整章节顺序');
  // TODO: 实现章节排序逻辑
};

// 添加小节
const addSection = (chapterId) => {
  console.log('添加小节到章节:', chapterId);
  // TODO: 实现添加小节逻辑
};

// 编辑小节
const editSection = (section) => {
  console.log('编辑小节:', section);
  // TODO: 实现编辑小节逻辑
};

// 删除小节
const deleteSection = (sectionId) => {
  console.log('删除小节:', sectionId);
  // TODO: 实现删除小节逻辑
};

// 组件挂载时加载数据
onMounted(() => {
  loadChapters();
});
</script>

<style scoped>
/* 章节管理样式 */
.course-chapters {
  background-color: var(--background-color, #ffffff);
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1.5rem;
  border-bottom: 1px solid var(--border-color, #e5e7eb);
  background-color: var(--background-color, #ffffff);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0;
}

.section-actions {
  display: flex;
  gap: 0.75rem;
}

.btn {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 1rem;
  border-radius: 0.375rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: 1px solid transparent;
}

.btn-primary {
  background-color: var(--primary-color, #6366f1);
  color: white;
}

.btn-primary:hover {
  background-color: var(--primary-color-dark, #4f46e5);
}

.btn-secondary {
  background-color: var(--background-color-secondary, #f3f4f6);
  color: var(--text-color, #374151);
  border-color: var(--border-color, #d1d5db);
}

.btn-secondary:hover {
  background-color: var(--background-color-tertiary, #e5e7eb);
}

/* 加载状态 */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  color: var(--text-color-secondary, #6b7280);
}

.loading-spinner {
  width: 2rem;
  height: 2rem;
  border: 2px solid var(--border-color, #e5e7eb);
  border-top: 2px solid var(--primary-color, #6366f1);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  to { transform: rotate(360deg); }
}

/* 章节列表 */
.chapters-list {
  padding: 1rem;
}

.chapter-item {
  border: 1px solid var(--border-color, #e5e7eb);
  border-radius: 0.5rem;
  margin-bottom: 1rem;
  overflow: hidden;
  transition: all 0.2s;
}

.chapter-item:hover {
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.chapter-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem;
  cursor: pointer;
  background-color: var(--background-color, #ffffff);
}

.chapter-info {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
}

.chapter-number {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--primary-color, #6366f1);
  background-color: var(--primary-color-light, #eef2ff);
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.chapter-title {
  font-size: 1rem;
  font-weight: 500;
  color: var(--text-color, #1f2937);
  margin: 0;
}

.chapter-status {
  font-size: 0.75rem;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-weight: 500;
}

.chapter-status.published {
  background-color: #dcfce7;
  color: #166534;
}

.chapter-status.draft {
  background-color: #fef3c7;
  color: #92400e;
}

.chapter-status.archived {
  background-color: #f3f4f6;
  color: #6b7280;
}

/* 空状态 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem;
  text-align: center;
}

.empty-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
}

.empty-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--text-color, #1f2937);
  margin: 0 0 0.5rem 0;
}

.empty-description {
  color: var(--text-color-secondary, #6b7280);
  margin: 0 0 1.5rem 0;
}
</style>
